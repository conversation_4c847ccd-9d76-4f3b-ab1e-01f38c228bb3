name: Reformat code

on:
  workflow_dispatch:
  push:
    branches:
      - a_stub_for_act
  pull_request:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

jobs:
  paths-filter:
    name: Need to run?
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions: {}
    outputs:
      pass: ${{ steps.filter.outputs.pass }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Filters
        uses: ./.github/actions/paths-filter
        id: filter
        with:
          extra-filter: ".github/workflows/reformat.yml,docs/source/notebooks/**,ansible/**"

  reformat:
    name: Reformat
    needs: paths-filter
    if: needs.paths-filter.outputs.pass == 'true'
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    container:
      image: ghcr.io/dr-qp/jazzy-ros-desktop:edge
    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the
      # added or changed files to the repository.
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          set-safe-directory: true

      - uses: getsentry/action-setup-venv@v2.1.1
        id: venv
        with:
          python-version: 3.12
          cache-dependency-path: |
            requirements.txt
            docs/requirements.txt
            docs/constraints.txt
            docs/source/notebooks/requirements.txt
          install-cmd: pip install -r requirements.txt

      - name: Reformat code
        shell: bash -le {0}
        run: |
          source /opt/ros/jazzy/setup.bash
          ./scripts/cpp-reformat.sh || true

          ./scripts/python-reformat.sh || true

      # Commit all changed files back to the repository
      - uses: stefanzweifel/git-auto-commit-action@v6
        if: ${{ github.event_name == 'pull_request' }}
        with:
          commit_message: Reformatted code
